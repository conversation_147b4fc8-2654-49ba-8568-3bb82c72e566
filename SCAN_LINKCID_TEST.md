# LinkCID扫描功能测试指南

## 功能概述

已成功添加`scan_linkcid`函数，支持BLE5扩展广播的扫描并返回LinkCID数据。该功能参考了esp-nimble-cpp的NimBLEScan类实现。

## 新增功能

### 1. 核心函数
- `int scan_linkcid(void)` - 启动扩展广播扫描
- `std::string ble_scan_linkcid_start_and_wait_json()` - 扫描并返回JSON结果

### 2. 数据结构增强
- `BleDeviceInfo`结构体新增`linkcid`字段
- 支持同时存储设备信息和LinkCID数据

### 3. 事件处理
- 新增`ble_gap_ext_event`函数处理`BLE_GAP_EVENT_EXT_DISC`事件
- 自动解析扩展广播数据并提取LinkCID

### 4. LinkCID提取
- `extract_linkcid_from_mfg_data`函数从制造商数据中提取LinkCID
- 支持制造商ID 0xFFFF格式的LinkCID数据

## 测试方法

### 自动测试
程序启动后会自动执行测试序列：

1. **启动LinkCID广播** (立即)
   ```
   Broadcasting LinkCID: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT
   ```

2. **重新启动不同LinkCID广播** (10秒后)
   ```
   New LinkCID: QmTestHashForBLE5ExtendedAdvertising123456789
   ```

3. **启动LinkCID扫描测试** (20秒后)
   ```
   === Starting LinkCID Scan Test ===
   LinkCID Scan Result: {"success":true,"count":1,"devices":[...]}
   ```

### 手动测试
在main.cc中已添加简单的测试代码：
```cpp
std::string result = ble_scan_linkcid_start_and_wait_json();
printf("扫描结果: %s\n", result.c_str());
```

## 预期输出

### 扫描启动日志
```
I (xxx) GAP: Extended scan for LinkCID started successfully
I (xxx) GAP: GAP EVENT EXT DISCOVERY
I (xxx) GAP: Extended Device 1 - RSSI: -45
I (xxx) GAP: Device Name: IPFS-BLE5-Node
I (xxx) GAP: *** LinkCID Found: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT ***
I (xxx) GAP: LinkCID Length: 46 bytes
```

### JSON结果格式
```json
{
  "success": true,
  "count": 1,
  "devices": [
    {
      "name": "IPFS-BLE5-Node",
      "addr": "AA:BB:CC:DD:EE:FF", 
      "rssi": -45,
      "linkcid": "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT"
    }
  ]
}
```

## 技术实现

### 扫描参数配置
```c
struct ble_gap_ext_disc_params uncoded_params;
uncoded_params.itvl = 0;      // 使用默认间隔
uncoded_params.window = 0;    // 使用默认窗口
uncoded_params.passive = 0;   // 主动扫描
```

### 扩展扫描API调用
```c
ble_gap_ext_disc(ble_addr_type, 
                 SCAN_DURATION_MS / 10,  // duration in 10ms units
                 0,                      // period (continuous)
                 1,                      // filter_duplicates
                 0,                      // filter_policy
                 0,                      // limited
                 &uncoded_params,        // uncoded PHY params
                 NULL,                   // coded PHY params
                 ble_gap_ext_event,      // callback
                 NULL);                  // callback arg
```

### LinkCID数据格式
```
制造商数据格式:
[0xFF][0xFF][LinkCID字符串...]
  |     |     |
  |     |     +-- LinkCID内容 (N字节)
  |     +-------- 制造商ID高字节
  +-------------- 制造商ID低字节
```

## 配置要求

确保以下配置已启用：
```
CONFIG_BT_NIMBLE_EXT_ADV=y
CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y  
CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650
```

## 故障排除

### 常见问题
1. **扫描启动失败**
   - 检查BLE5配置是否正确
   - 确认没有其他扫描正在进行

2. **没有检测到LinkCID**
   - 确认附近有LinkCID广播设备
   - 检查制造商ID是否为0xFFFF

3. **JSON解析错误**
   - 检查返回的JSON格式
   - 确认cJSON库正常工作

### 调试建议
- 查看扫描日志中的制造商数据输出
- 验证扩展广播数据解析是否正确
- 检查LinkCID提取逻辑

## 与esp-nimble-cpp的对应关系

| 本实现 | esp-nimble-cpp | 说明 |
|--------|----------------|------|
| `scan_linkcid()` | `NimBLEScan::start()` | 启动扫描 |
| `ble_gap_ext_event` | `NimBLEScanCallbacks` | 扫描回调 |
| `extract_linkcid_from_mfg_data` | `NimBLEAdvertisedDevice::getManufacturerData()` | 数据提取 |
| `BleDeviceInfo` | `NimBLEAdvertisedDevice` | 设备信息 |

## 下一步扩展

可以考虑添加以下功能：
- 支持过滤特定LinkCID格式
- 添加连续扫描模式
- 支持扫描结果缓存
- 添加RSSI阈值过滤
- 支持多PHY扫描(1M/2M/Coded)
