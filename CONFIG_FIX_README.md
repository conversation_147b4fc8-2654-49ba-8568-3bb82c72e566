# LinkCID扫描配置修复说明

## 问题描述

在运行LinkCID扫描功能时遇到以下错误：
```
E (15496) NimBLE: Received HCI data length at host (96) exceeds maximum configured HCI event buffer size (70).
```

这个错误表明接收到的HCI数据包(96字节)超过了配置的最大HCI事件缓冲区大小(70字节)。

## 根本原因

BLE5扩展广播可以携带比传统广播更多的数据，因此需要更大的缓冲区来处理这些数据包。默认的NimBLE配置针对传统广播进行了优化，缓冲区大小不足以处理扩展广播数据。

## 配置修复

### 1. HCI事件缓冲区大小
**问题**: `CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE=70` 太小
**修复**: 
```
CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE=257
```

### 2. 扩展广播最大数据大小
**问题**: `CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=53` 太小
**修复**:
```
CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650
```

## 完整的必需配置

确保以下配置在`sdkconfig`中正确设置：

```ini
# BLE5功能支持
CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y

# 扩展广播支持
CONFIG_BT_NIMBLE_EXT_ADV=y
CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650
CONFIG_BT_NIMBLE_MAX_EXT_ADV_INSTANCES=1

# 扩展扫描支持
CONFIG_BT_NIMBLE_EXT_SCAN=y

# 缓冲区配置
CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE=257
CONFIG_BT_NIMBLE_ACL_BUF_SIZE=255
CONFIG_BT_NIMBLE_ACL_BUF_COUNT=24

# PHY支持
CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY=y
CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY=y

# 周期性广播支持(可选)
CONFIG_BT_NIMBLE_ENABLE_PERIODIC_ADV=y
CONFIG_BT_NIMBLE_ENABLE_PERIODIC_SYNC=y
CONFIG_BT_NIMBLE_PERIODIC_ADV_SYNC_TRANSFER=y
```

## 配置说明

### HCI事件缓冲区大小 (257字节)
- **用途**: 处理来自控制器的HCI事件
- **原因**: 扩展广播报告可能包含大量数据
- **影响**: 增加内存使用，但确保能处理大型广播包

### 扩展广播最大大小 (1650字节)
- **用途**: 设置扩展广播数据的最大大小
- **原因**: 支持大型LinkCID字符串(如IPFS哈希)
- **影响**: 允许广播和接收大容量数据

### ACL缓冲区配置
- **ACL_BUF_SIZE=255**: 足够处理标准数据包
- **ACL_BUF_COUNT=24**: 提供足够的缓冲区数量

## 内存影响

这些配置更改会增加内存使用：

| 配置项 | 原值 | 新值 | 内存增加 |
|--------|------|------|----------|
| HCI_EVT_BUF_SIZE | 70 | 257 | +187字节/缓冲区 |
| EXT_ADV_MAX_SIZE | 53 | 1650 | +1597字节 |

**总内存增加**: 约2KB (取决于缓冲区数量)

## 验证配置

配置修改后，重新编译并运行程序：

```bash
idf.py build
idf.py flash monitor
```

### 预期结果
- 不再出现HCI缓冲区溢出错误
- 能够成功接收和解析扩展广播数据
- LinkCID扫描功能正常工作

### 成功日志示例
```
I (xxx) GAP: Extended scan for LinkCID started successfully
I (xxx) GAP: GAP EVENT EXT DISCOVERY
I (xxx) GAP: *** LinkCID Found: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT ***
```

## 故障排除

### 如果仍有缓冲区错误
1. 进一步增加`CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE`
2. 增加`CONFIG_BT_NIMBLE_HCI_EVT_HI_BUF_COUNT`
3. 检查`CONFIG_BT_NIMBLE_MSYS1_BLOCK_COUNT`

### 如果内存不足
1. 减少其他组件的内存使用
2. 优化任务栈大小
3. 考虑使用PSRAM

### 配置工具
也可以使用menuconfig进行配置：
```bash
idf.py menuconfig
```
导航到: `Component config → Bluetooth → NimBLE Options`

## 注意事项

⚠️ **重要**: 这些配置更改会增加内存使用，确保ESP32有足够的可用内存

⚠️ **兼容性**: 这些设置针对BLE5扩展广播优化，可能影响传统BLE应用的性能

⚠️ **功耗**: 更大的缓冲区可能会略微增加功耗

## 相关文档

- [ESP-IDF NimBLE配置](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/bluetooth/nimble/index.html)
- [NimBLE扩展广播文档](https://mynewt.apache.org/v1_8_0/network/ble_hs/ble_gap.html)
- [BLE5规范](https://www.bluetooth.com/specifications/bluetooth-core-specification/)
