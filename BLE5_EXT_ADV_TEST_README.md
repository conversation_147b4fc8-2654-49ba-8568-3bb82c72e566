# BLE5扩展广播测试说明

## 功能概述

已在main.cc中添加了BLE5扩展广播的完整测试代码，用于广播字符串"QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT"。

## 测试流程

### 1. 启动时自动测试
程序启动后会自动执行以下步骤：

1. **初始化BLE栈**
2. **启动扩展广播**
   - UUID: `6E400001-B5A3-F393-E0A9-E50E24DCCA9E`
   - 数据: `QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT`
   - 设备名: `IPFS-BLE5-Node`
3. **启动测试任务**

### 2. 自动测试序列
测试任务会在10秒后执行：

1. **停止当前广播** (10秒后)
2. **等待5秒**
3. **重新启动广播** (使用不同数据)
   - 新数据: `QmTestHashForBLE5ExtendedAdvertising123456789`
   - 新设备名: `BLE5-Test-Node`

## 预期日志输出

```
I (xxx) MAIN: Starting BLE5 Extended Advertising test...
I (xxx) MAIN: Broadcasting IPFS hash: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT
I (xxx) MAIN: String length: 46 bytes
I (xxx) MAIN: Device name: IPFS-BLE5-Node
I (xxx) EXT_ADV: Extended advertising started successfully
I (xxx) MAIN: ✓ BLE5 Extended Advertising started successfully!
I (xxx) MAIN:   - Using BLE5 Extended Advertising PDU
I (xxx) MAIN:   - Data in manufacturer field (ID: 0xFFFF)
I (xxx) MAIN:   - Continuous advertising mode
I (xxx) MAIN: Started extended advertising test task

... (10秒后) ...

I (xxx) MAIN: === Extended Advertising Test ===
I (xxx) MAIN: Stopping extended advertising...
I (xxx) EXT_ADV: Extended advertising stopped successfully
I (xxx) MAIN: ✓ Extended advertising stopped

... (5秒后) ...

I (xxx) MAIN: Restarting extended advertising with different data...
I (xxx) EXT_ADV: Extended advertising started successfully
I (xxx) MAIN: ✓ Extended advertising restarted with new data
I (xxx) MAIN: New data: QmTestHashForBLE5ExtendedAdvertising123456789
```

## 编译和运行

### 1. 编译
```bash
idf.py build
```

### 2. 烧录
```bash
idf.py flash
```

### 3. 监控日志
```bash
idf.py monitor
```

## 验证方法

### 使用BLE扫描工具验证：

1. **nRF Connect (手机应用)**
   - 扫描BLE设备
   - 查找设备名 "IPFS-BLE5-Node"
   - 查看制造商数据字段
   - 应该能看到完整的IPFS哈希字符串

2. **ESP32扫描设备**
   - 使用另一个ESP32运行扫描程序
   - 查看制造商数据输出

3. **命令行工具**
   ```bash
   # Linux下使用hcitool
   sudo hcitool lescan
   sudo hcidump -R
   ```

## 广播数据格式

扩展广播包含以下字段：
- **标志**: 0x06 (一般可发现 + 不支持BR/EDR)
- **完整本地名称**: "IPFS-BLE5-Node" 或 "BLE5-Test-Node"
- **完整128位服务UUID**: 指定的UUID
- **制造商数据**: 
  - 制造商ID: 0xFFFF (2字节)
  - 数据: IPFS哈希字符串 (46字节)

## 配置要求

确保以下配置已启用：
- `CONFIG_BT_NIMBLE_EXT_ADV=y`
- `CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y`
- `CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650`

## 故障排除

如果看到以下错误：
- "BLE5 Extended Advertising not supported": 检查CONFIG_BT_NIMBLE_EXT_ADV配置
- "Failed to start extended advertising": 检查UUID和数据长度
- "Failed to configure extended advertising": 检查BLE5硬件支持

## API使用示例

```c
// 定义UUID
uint8_t uuid[16] = {0x6E, 0x40, 0x00, 0x01, ...};

// 启动扩展广播
int result = ble_start_ext_advertising_v1(
    uuid, 
    "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT", 
    "IPFS-BLE5-Node"
);

// 停止扩展广播
ble_stop_ext_advertising();
```
