# LinkCID BLE5扩展广播扫描功能

## 功能概述

新增的`scan_linkcid`函数支持扫描BLE5扩展广播并提取其中的LinkCID数据。该功能基于esp-nimble-cpp的扩展广播扫描API实现。

## API函数

### C接口
```c
int scan_linkcid(void);
```

### C++接口
```cpp
std::string ble_scan_linkcid_start_and_wait_json();
```

## 功能特性

✅ **BLE5扩展广播扫描**: 使用`ble_gap_ext_disc`进行扩展广播扫描  
✅ **LinkCID提取**: 自动从制造商数据中提取LinkCID字符串  
✅ **JSON结果**: 返回包含设备信息和LinkCID的JSON格式结果  
✅ **兼容性**: 同时支持传统广播和扩展广播的LinkCID检测  

## 使用示例

### 基本使用
```c
// 启动扫描
int result = scan_linkcid();
if (result == 0) {
    printf("扫描启动成功\n");
} else {
    printf("扫描启动失败: %d\n", result);
}
```

### 获取JSON结果
```cpp
// 启动扫描并获取JSON结果
std::string result = ble_scan_linkcid_start_and_wait_json();
printf("扫描结果: %s\n", result.c_str());
```

## JSON结果格式

```json
{
  "success": true,
  "count": 2,
  "devices": [
    {
      "name": "IPFS-BLE5-Node",
      "addr": "AA:BB:CC:DD:EE:FF",
      "rssi": -45,
      "linkcid": "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT"
    },
    {
      "name": "BLE5-Test-Node", 
      "addr": "11:22:33:44:55:66",
      "rssi": -52,
      "linkcid": "QmTestHashForBLE5ExtendedAdvertising123456789"
    }
  ]
}
```

## 字段说明

- **success**: 扫描是否成功
- **count**: 发现的设备数量
- **devices**: 设备列表数组
  - **name**: 设备名称
  - **addr**: 设备MAC地址
  - **rssi**: 信号强度(dBm)
  - **linkcid**: 提取的LinkCID字符串(如果没有则为空字符串)

## 实现原理

### 扫描流程
1. 使用`ble_gap_ext_disc`启动BLE5扩展广播扫描
2. 监听`BLE_GAP_EVENT_EXT_DISC`事件
3. 解析扩展广播数据包
4. 从制造商数据中提取LinkCID
5. 将结果存储并返回JSON格式

### LinkCID提取
- 检查制造商ID是否为`0xFFFF`
- 提取制造商数据中的LinkCID字符串
- 支持最大1600字节的LinkCID数据

### 数据格式
```
[制造商ID: 0xFFFF][LinkCID字符串]
    2字节           N字节
```

## 配置要求

确保以下配置已启用：
- `CONFIG_BT_NIMBLE_EXT_ADV=y`
- `CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y`
- `CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650`

## 测试验证

### 自动测试
程序启动后会自动执行以下测试序列：
1. 启动LinkCID广播
2. 等待20秒让广播稳定
3. 启动LinkCID扫描
4. 显示扫描结果

### 预期日志
```
I (xxx) MAIN: Starting LinkCID advertising...
I (xxx) MAIN: ✓ LinkCID advertising started successfully!
I (xxx) MAIN: Started LinkCID scan test task
I (xxx) MAIN: === Starting LinkCID Scan Test ===
I (xxx) GAP: Extended scan for LinkCID started successfully
I (xxx) GAP: GAP EVENT EXT DISCOVERY
I (xxx) GAP: *** LinkCID Found: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT ***
I (xxx) MAIN: LinkCID Scan Result: {"success":true,"count":1,"devices":[...]}
```

## 错误处理

### 常见错误
- **扫描启动失败**: 检查BLE5配置是否正确
- **没有发现LinkCID**: 确认附近有LinkCID广播设备
- **JSON解析失败**: 检查返回的JSON格式

### 调试建议
1. 检查ESP32是否支持BLE5
2. 确认CONFIG_BT_NIMBLE_EXT_ADV已启用
3. 查看扫描日志中的制造商数据
4. 验证LinkCID格式是否正确

## 与传统扫描的区别

| 特性 | 传统扫描 | 扩展广播扫描 |
|------|----------|-------------|
| API | `ble_gap_disc` | `ble_gap_ext_disc` |
| 事件 | `BLE_GAP_EVENT_DISC` | `BLE_GAP_EVENT_EXT_DISC` |
| 数据容量 | 31字节 | 1650字节 |
| PHY支持 | 1M | 1M/2M/Coded |
| LinkCID支持 | 有限 | 完整 |

## 注意事项

⚠️ **重要**: 扫描和广播不能同时进行，需要停止广播后才能启动扫描  
⚠️ **性能**: 扩展扫描比传统扫描消耗更多资源  
⚠️ **兼容性**: 需要BLE5.0及以上版本支持  
