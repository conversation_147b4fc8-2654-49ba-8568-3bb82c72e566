#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <cJSON.h>
#include <string>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;

#if CONFIG_BT_NIMBLE_EXT_ADV
// 测试任务：演示LinkCID扫描功能
void scan_linkcid_test_task(void *param)
{
    vTaskDelay(pdMS_TO_TICKS(15000)); // 等待15秒让广播稳定

    ESP_LOGI(TAG, "=== LinkCID Scanning Test ===");
    ESP_LOGI(TAG, "Starting extended scan to detect LinkCID advertisements...");

    // 启动LinkCID扫描并等待结果
    std::string scan_result = ble_scan_linkcid_start_and_wait_json();

    ESP_LOGI(TAG, "=== LinkCID Scan Results ===");
    ESP_LOGI(TAG, "JSON Result: %s", scan_result.c_str());

    // 解析并显示结果
    cJSON *root = cJSON_Parse(scan_result.c_str());
    if (root) {
        cJSON *success = cJSON_GetObjectItem(root, "success");
        cJSON *count = cJSON_GetObjectItem(root, "count");
        cJSON *devices = cJSON_GetObjectItem(root, "devices");

        if (cJSON_IsTrue(success)) {
            int device_count = cJSON_GetNumberValue(count);
            ESP_LOGI(TAG, "✓ Scan completed successfully, found %d devices", device_count);

            if (cJSON_IsArray(devices)) {
                cJSON *device = NULL;
                int index = 0;
                cJSON_ArrayForEach(device, devices) {
                    cJSON *name = cJSON_GetObjectItem(device, "name");
                    cJSON *addr = cJSON_GetObjectItem(device, "addr");
                    cJSON *rssi = cJSON_GetObjectItem(device, "rssi");
                    cJSON *linkcid = cJSON_GetObjectItem(device, "linkcid");

                    ESP_LOGI(TAG, "Device %d:", index + 1);
                    ESP_LOGI(TAG, "  Name: %s", cJSON_GetStringValue(name));
                    ESP_LOGI(TAG, "  Address: %s", cJSON_GetStringValue(addr));
                    ESP_LOGI(TAG, "  RSSI: %d", (int)cJSON_GetNumberValue(rssi));

                    const char* linkcid_str = cJSON_GetStringValue(linkcid);
                    if (linkcid_str && strlen(linkcid_str) > 0) {
                        ESP_LOGI(TAG, "  *** LinkCID: %s ***", linkcid_str);
                        ESP_LOGI(TAG, "  *** LinkCID Length: %zu bytes ***", strlen(linkcid_str));
                    } else {
                        ESP_LOGI(TAG, "  LinkCID: (none)");
                    }
                    index++;
                }
            }
        } else {
            ESP_LOGE(TAG, "✗ Scan failed");
        }

        cJSON_Delete(root);
    } else {
        ESP_LOGE(TAG, "Failed to parse scan result JSON");
    }

    ESP_LOGI(TAG, "=== LinkCID Scan Test Completed ===");

    // 任务完成，删除自己
    vTaskDelete(NULL);
}

// 测试任务：演示停止和重新启动扩展广播
void ext_adv_test_task(void *param)
{
    vTaskDelay(pdMS_TO_TICKS(10000)); // 等待10秒

    ESP_LOGI(TAG, "=== LinkCID Advertising Test ===");
    ESP_LOGI(TAG, "Testing with different LinkCID...");

    vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒

    ESP_LOGI(TAG, "Starting advertising with different LinkCID...");

    // 使用不同的LinkCID重新启动
    const char* test_linkcid = "QmTestHashForBLE5ExtendedAdvertising123456789";

    int result = adv_linkcid(test_linkcid);
    if (result == 0) {
        ESP_LOGI(TAG, "✓ LinkCID advertising restarted with new data");
        ESP_LOGI(TAG, "New LinkCID: %s", test_linkcid);
        ESP_LOGI(TAG, "New payload size: %zu bytes", strlen(test_linkcid) + 2);

        // 启动扫描测试任务
        xTaskCreate(scan_linkcid_test_task, "scan_linkcid_test", 8192, NULL, 5, NULL);
        ESP_LOGI(TAG, "Started LinkCID scan test task");
    } else {
        ESP_LOGE(TAG, "✗ Failed to restart LinkCID advertising: %d", result);
    }

    // 任务完成，删除自己
    vTaskDelete(NULL);
}
#endif

// Test function to demonstrate advertising
extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service

    // Set a custom sync callback that will schedule extended advertising
    ble_hs_cfg.sync_cb = [](void) {
        ble_hs_id_infer_auto(0, &ble_addr_type);
        ESP_LOGI(TAG, "BLE Host synchronized, scheduling extended advertising...");
        std::string result = ble_scan_linkcid_start_and_wait_json();
        printf("扫描结果: %s\n", result.c_str());
#if CONFIG_BT_NIMBLE_EXT_ADV
        // 创建一个任务来启动LinkCID广播，避免在同步回调中直接调用
        /** 
        xTaskCreate([](void* param) {
            vTaskDelay(pdMS_TO_TICKS(100)); // 等待同步完成

            ESP_LOGI(TAG, "Starting LinkCID advertising...");

            // 要广播的LinkCID
            const char* linkcid = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";

            ESP_LOGI(TAG, "Broadcasting LinkCID: %s", linkcid);
            ESP_LOGI(TAG, "LinkCID length: %zu bytes", strlen(linkcid));

            // 启动LinkCID广播
            int result = adv_linkcid(linkcid);

            if (result == 0) {
                ESP_LOGI(TAG, "✓ LinkCID advertising started successfully!");
                ESP_LOGI(TAG, "  - Using BLE5 Extended Advertising");
                ESP_LOGI(TAG, "  - LinkCID in manufacturer data (ID: 0xFFFF)");
                ESP_LOGI(TAG, "  - Total payload: %zu bytes", strlen(linkcid) + 2);

                // 启动测试任务
                xTaskCreate(ext_adv_test_task, "ext_adv_test", 4096, NULL, 5, NULL);
                ESP_LOGI(TAG, "Started advertising test task");

                // 启动LinkCID扫描测试任务
                xTaskCreate([](void* param) {
                    vTaskDelay(pdMS_TO_TICKS(20000)); // 等待20秒让广播稳定

                    ESP_LOGI(TAG, "=== Starting LinkCID Scan Test ===");

                    // 启动LinkCID扫描
                    std::string scan_result = ble_scan_linkcid_start_and_wait_json();
                    ESP_LOGI(TAG, "LinkCID Scan Result: %s", scan_result.c_str());

                    vTaskDelete(NULL);
                }, "scan_linkcid_test", 8192, NULL, 5, NULL);
                ESP_LOGI(TAG, "Started LinkCID scan test task");
            } else {
                ESP_LOGE(TAG, "✗ Failed to start LinkCID advertising: %d", result);
            }

            // 删除这个临时任务
            vTaskDelete(NULL);
        }, "start_linkcid_adv", 4096, NULL, 5, NULL);*/
#else
        ESP_LOGW(TAG, "BLE5 Extended Advertising not supported - CONFIG_BT_NIMBLE_EXT_ADV not enabled");
        ESP_LOGI(TAG, "Starting regular scan instead...");
        // 如果不支持扩展广播，则启动扫描
        xTaskCreate([](void* param) {
            vTaskDelay(pdMS_TO_TICKS(100));
            ble_app_scan();
            vTaskDelete(NULL);
        }, "start_scan", 2048, NULL, 5, NULL);
#endif
    };

    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
