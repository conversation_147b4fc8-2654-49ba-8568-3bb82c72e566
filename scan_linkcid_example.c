/*
 * LinkCID BLE5扩展广播扫描功能使用示例
 * 
 * 此示例展示如何使用scan_linkcid函数进行BLE5扩展广播扫描
 * 并从扫描结果中提取LinkCID数据
 */

#include "link_protocol.h"
#include <cJSON.h>
#include <string.h>

#if CONFIG_BT_NIMBLE_EXT_ADV

// 简单的扫描示例
void example_simple_scan_linkcid() {
    printf("=== 简单LinkCID扫描示例 ===\n");
    
    // 启动扫描
    int result = scan_linkcid();
    
    if (result == 0) {
        printf("✓ LinkCID扫描启动成功\n");
        printf("扫描将持续3秒...\n");
    } else {
        printf("✗ LinkCID扫描启动失败: %d\n", result);
    }
}

// 完整的扫描和结果处理示例
void example_full_scan_linkcid() {
    printf("=== 完整LinkCID扫描示例 ===\n");
    
    // 启动扫描并获取JSON结果
    std::string scan_result = ble_scan_linkcid_start_and_wait_json();
    
    printf("原始JSON结果: %s\n", scan_result.c_str());
    
    // 解析JSON结果
    cJSON *root = cJSON_Parse(scan_result.c_str());
    if (root == NULL) {
        printf("✗ JSON解析失败\n");
        return;
    }
    
    // 检查扫描是否成功
    cJSON *success = cJSON_GetObjectItem(root, "success");
    if (!cJSON_IsTrue(success)) {
        printf("✗ 扫描失败\n");
        cJSON_Delete(root);
        return;
    }
    
    // 获取设备数量
    cJSON *count = cJSON_GetObjectItem(root, "count");
    int device_count = cJSON_GetNumberValue(count);
    printf("✓ 扫描成功，发现 %d 个设备\n", device_count);
    
    // 遍历设备列表
    cJSON *devices = cJSON_GetObjectItem(root, "devices");
    if (cJSON_IsArray(devices)) {
        cJSON *device = NULL;
        int index = 0;
        
        cJSON_ArrayForEach(device, devices) {
            printf("\n--- 设备 %d ---\n", index + 1);
            
            // 获取设备信息
            cJSON *name = cJSON_GetObjectItem(device, "name");
            cJSON *addr = cJSON_GetObjectItem(device, "addr");
            cJSON *rssi = cJSON_GetObjectItem(device, "rssi");
            cJSON *linkcid = cJSON_GetObjectItem(device, "linkcid");
            
            printf("设备名称: %s\n", cJSON_GetStringValue(name));
            printf("MAC地址: %s\n", cJSON_GetStringValue(addr));
            printf("信号强度: %d dBm\n", (int)cJSON_GetNumberValue(rssi));
            
            // 检查是否有LinkCID
            const char* linkcid_str = cJSON_GetStringValue(linkcid);
            if (linkcid_str && strlen(linkcid_str) > 0) {
                printf("*** LinkCID: %s ***\n", linkcid_str);
                printf("*** LinkCID长度: %zu 字节 ***\n", strlen(linkcid_str));
                
                // 检查是否是IPFS哈希
                if (strncmp(linkcid_str, "Qm", 2) == 0) {
                    printf("*** 检测到IPFS哈希 ***\n");
                }
            } else {
                printf("LinkCID: (无)\n");
            }
            
            index++;
        }
    }
    
    // 清理JSON对象
    cJSON_Delete(root);
    
    printf("\n=== 扫描完成 ===\n");
}

// 过滤LinkCID设备的示例
void example_filter_linkcid_devices() {
    printf("=== LinkCID设备过滤示例 ===\n");
    
    // 启动扫描
    std::string scan_result = ble_scan_linkcid_start_and_wait_json();
    
    // 解析结果
    cJSON *root = cJSON_Parse(scan_result.c_str());
    if (root == NULL) {
        printf("✗ JSON解析失败\n");
        return;
    }
    
    cJSON *success = cJSON_GetObjectItem(root, "success");
    if (!cJSON_IsTrue(success)) {
        printf("✗ 扫描失败\n");
        cJSON_Delete(root);
        return;
    }
    
    // 统计有LinkCID的设备
    int total_devices = 0;
    int linkcid_devices = 0;
    
    cJSON *devices = cJSON_GetObjectItem(root, "devices");
    if (cJSON_IsArray(devices)) {
        cJSON *device = NULL;
        
        cJSON_ArrayForEach(device, devices) {
            total_devices++;
            
            cJSON *linkcid = cJSON_GetObjectItem(device, "linkcid");
            const char* linkcid_str = cJSON_GetStringValue(linkcid);
            
            if (linkcid_str && strlen(linkcid_str) > 0) {
                linkcid_devices++;
                
                cJSON *name = cJSON_GetObjectItem(device, "name");
                cJSON *addr = cJSON_GetObjectItem(device, "addr");
                
                printf("LinkCID设备 %d:\n", linkcid_devices);
                printf("  名称: %s\n", cJSON_GetStringValue(name));
                printf("  地址: %s\n", cJSON_GetStringValue(addr));
                printf("  LinkCID: %s\n", linkcid_str);
                printf("\n");
            }
        }
    }
    
    printf("扫描统计:\n");
    printf("  总设备数: %d\n", total_devices);
    printf("  LinkCID设备数: %d\n", linkcid_devices);
    printf("  LinkCID设备比例: %.1f%%\n", 
           total_devices > 0 ? (float)linkcid_devices / total_devices * 100 : 0);
    
    cJSON_Delete(root);
}

// 连续扫描示例
void example_continuous_scan() {
    printf("=== 连续扫描示例 ===\n");
    
    for (int i = 1; i <= 3; i++) {
        printf("\n--- 第 %d 次扫描 ---\n", i);
        
        std::string result = ble_scan_linkcid_start_and_wait_json();
        
        // 简单统计
        cJSON *root = cJSON_Parse(result.c_str());
        if (root) {
            cJSON *count = cJSON_GetObjectItem(root, "count");
            int device_count = cJSON_GetNumberValue(count);
            printf("发现设备: %d 个\n", device_count);
            cJSON_Delete(root);
        }
        
        // 间隔2秒
        if (i < 3) {
            printf("等待2秒后进行下次扫描...\n");
            vTaskDelay(pdMS_TO_TICKS(2000));
        }
    }
    
    printf("\n连续扫描完成\n");
}

#else
void example_scan_not_supported() {
    printf("BLE5扩展广播扫描功能未启用\n");
    printf("请在menuconfig中启用 CONFIG_BT_NIMBLE_EXT_ADV\n");
}
#endif // CONFIG_BT_NIMBLE_EXT_ADV

/*
 * 使用说明：
 * 
 * 1. 确保CONFIG_BT_NIMBLE_EXT_ADV已启用
 * 2. 调用相应的示例函数
 * 3. 扫描结果包含设备信息和LinkCID数据
 * 4. 可以根据需要过滤和处理LinkCID设备
 * 
 * 注意事项：
 * - 扫描和广播不能同时进行
 * - 扫描持续时间为3秒（可在SCAN_DURATION_MS中配置）
 * - LinkCID从制造商数据（ID: 0xFFFF）中提取
 * - 支持最大1600字节的LinkCID数据
 */
