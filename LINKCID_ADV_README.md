# LinkCID BLE5扩展广播

## 功能概述

简化的BLE5扩展广播实现，专门用于广播LinkCID字符串。

## API函数

### 唯一函数
```c
int adv_linkcid(const char *linkcid);
```

**参数**:
- `linkcid`: 要广播的LinkCID字符串（最大1600字节）

**返回值**:
- `0`: 成功
- `-1`: 失败

## 使用示例

```c
// 广播IPFS哈希
int result = adv_linkcid("QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT");

if (result == 0) {
    printf("LinkCID广播启动成功\n");
} else {
    printf("LinkCID广播启动失败\n");
}
```

## 广播数据格式

LinkCID被放在制造商数据字段中：

```
[AD Length] [AD Type] [制造商ID] [LinkCID字符串]
     1字节     1字节      2字节      N字节
```

- **AD Type**: 0xFF (制造商数据)
- **制造商ID**: 0xFFFF
- **LinkCID**: 原始字符串数据

## 数据大小计算

对于LinkCID "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT":
- LinkCID长度: 46字节
- 制造商ID: 2字节
- **总制造商数据**: 48字节
- **完整广播包**: ~50字节

## 测试流程

程序启动后自动执行：

1. **初始广播** (启动后100ms)
   - LinkCID: `QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT`

2. **测试切换** (10秒后)
   - 新LinkCID: `QmTestHashForBLE5ExtendedAdvertising123456789`

## 编译和运行

```bash
idf.py build
idf.py flash
idf.py monitor
```

## 预期日志

```
I (xxx) MAIN: Starting LinkCID advertising...
I (xxx) MAIN: Broadcasting LinkCID: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT
I (xxx) MAIN: LinkCID length: 46 bytes
I (xxx) ADV: LinkCID advertising started: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT (46 bytes)
I (xxx) MAIN: ✓ LinkCID advertising started successfully!
I (xxx) MAIN:   - Using BLE5 Extended Advertising
I (xxx) MAIN:   - LinkCID in manufacturer data (ID: 0xFFFF)
I (xxx) MAIN:   - Total payload: 48 bytes

... (10秒后) ...

I (xxx) MAIN: === LinkCID Advertising Test ===
I (xxx) MAIN: Testing with different LinkCID...
I (xxx) MAIN: Starting advertising with different LinkCID...
I (xxx) ADV: LinkCID advertising started: QmTestHashForBLE5ExtendedAdvertising123456789 (45 bytes)
I (xxx) MAIN: ✓ LinkCID advertising restarted with new data
I (xxx) MAIN: New LinkCID: QmTestHashForBLE5ExtendedAdvertising123456789
I (xxx) MAIN: New payload size: 47 bytes
```

## 特性

✅ **BLE5扩展广播**: 使用扩展广播PDU，不是传统广播  
✅ **大容量**: 支持最大1600字节LinkCID  
✅ **简单API**: 只需一个函数调用  
✅ **自动管理**: 自动处理实例创建和数据设置  
✅ **无死锁**: 正确处理BLE主机锁  

## 配置要求

- `CONFIG_BT_NIMBLE_EXT_ADV=y`
- `CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y`
- `CONFIG_BT_NIMBLE_EXT_ADV_MAX_SIZE=1650`

## 扫描验证

使用BLE扫描工具查看：
- 设备类型: 扩展广播设备
- 制造商数据: 0xFFFF + LinkCID字符串
- 数据长度: LinkCID长度 + 2字节
