/*
 * BLE5扩展广播功能使用示例
 * 
 * 此示例展示如何使用ble_start_ext_advertising_v1函数进行BLE5扩展广播
 * 参数：128位UUID + 字符串数据 + 设备名称
 */

#include "link_protocol.h"
#include <string.h>

#if CONFIG_BT_NIMBLE_EXT_ADV

void example_start_ext_advertising_v1() {
    // 定义128位UUID (16字节)
    uint8_t my_uuid[16] = {
        0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
        0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78
    };
    
    // 要广播的字符串数据
    const char* data_string = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    const char* device_name = "BLE5-ExtAdv-Device";
    
    // 开始扩展广播
    int result = ble_start_ext_advertising_v1(my_uuid, data_string, device_name);
    
    if (result == 0) {
        printf("BLE5扩展广播启动成功\n");
        printf("广播字符串: %s\n", data_string);
        printf("字符串长度: %zu 字节\n", strlen(data_string));
    } else {
        printf("BLE5扩展广播启动失败: %d\n", result);
    }
}

void example_stop_ext_advertising() {
    // 停止扩展广播
    int result = ble_stop_ext_advertising();
    
    if (result == 0) {
        printf("BLE5扩展广播停止成功\n");
    } else {
        printf("BLE5扩展广播停止失败: %d\n", result);
    }
}

// 完整的使用示例
void example_full_ext_advertising_demo() {
    printf("=== BLE5扩展广播演示 ===\n");
    
    // 定义UUID
    uint8_t uuid[16] = {
        0x6E, 0x40, 0x00, 0x01, 0xB5, 0xA3, 0xF3, 0x93,
        0xE0, 0xA9, 0xE5, 0x0E, 0x24, 0xDC, 0xCA, 0x9E
    };
    
    // 要广播的IPFS哈希字符串
    const char* ipfs_hash = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    const char* device_name = "IPFS-BLE5-Node";
    
    printf("开始BLE5扩展广播...\n");
    printf("IPFS哈希: %s\n", ipfs_hash);
    printf("设备名称: %s\n", device_name);
    
    int result = ble_start_ext_advertising_v1(uuid, ipfs_hash, device_name);
    
    if (result == 0) {
        printf("✓ 扩展广播启动成功！\n");
        printf("  - 使用BLE5扩展广播PDU\n");
        printf("  - 字符串长度: %zu 字节\n", strlen(ipfs_hash));
        printf("  - 广播实例ID: 0\n");
        printf("  - 广播模式: 持续广播\n");
        
        // 运行一段时间后停止
        printf("广播将持续运行，调用 ble_stop_ext_advertising() 来停止\n");
    } else {
        printf("✗ 扩展广播启动失败: %d\n", result);
    }
}

#else
void example_ext_advertising_not_supported() {
    printf("BLE5扩展广播功能未启用\n");
    printf("请在menuconfig中启用 CONFIG_BT_NIMBLE_EXT_ADV\n");
}
#endif // CONFIG_BT_NIMBLE_EXT_ADV

/*
 * 使用说明：
 * 
 * 1. 确保CONFIG_BT_NIMBLE_EXT_ADV已启用
 * 2. 调用 ble_start_ext_advertising_v1() 开始广播
 * 3. 字符串数据将作为制造商数据在扩展广播中发送
 * 4. 扫描设备可以接收到完整的字符串数据
 * 5. 调用 ble_stop_ext_advertising() 停止广播
 * 
 * 特点：
 * - 支持最大1600字节的字符串数据
 * - 使用BLE5扩展广播PDU，不是传统广播
 * - 字符串数据放在制造商数据字段中
 * - 同时包含128位服务UUID和设备名称
 */
